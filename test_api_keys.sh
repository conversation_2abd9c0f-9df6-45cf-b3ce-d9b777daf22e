#!/bin/bash

# API Key Testing Script for OpenCode
# This script tests each API key to see which ones are working

echo "🔑 Testing API Keys for OpenCode..."
echo "=================================="

# Source the environment file
source ~/.opencode.env

# Test results array
declare -A test_results

# Function to test an API key with OpenCode
test_api_key() {
    local provider=$1
    local model=$2
    local api_key_var=$3
    
    echo "Testing $provider with model $model..."
    
    # Create temporary config for this test
    cat > /tmp/test_opencode.json << EOF
{
  "autoCompact": true,
  "providers": {
    "$provider": {
      "apiKey": "${!api_key_var}",
      "disabled": false
    }
  },
  "agents": {
    "coder": {
      "model": "$model",
      "maxTokens": 100
    }
  },
  "mcpServers": {}
}
EOF
    
    # Test with a simple prompt
    timeout 30s opencode -p "Say 'Hello' in one word only" -f json 2>&1 | grep -q "Hello\|hello\|HELLO"
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo "✅ $provider ($model) - WORKING"
        test_results[$provider]="✅ WORKING"
    else
        echo "❌ $provider ($model) - FAILED"
        test_results[$provider]="❌ FAILED"
    fi
    
    # Clean up
    rm -f /tmp/test_opencode.json
    echo ""
}

# Test OpenAI
echo "1. Testing OpenAI..."
export OPENCODE_CONFIG="/tmp/test_opencode.json"
test_api_key "openai" "gpt-4o-mini" "OPENAI_API_KEY"

# Test Anthropic
echo "2. Testing Anthropic..."
test_api_key "anthropic" "claude-3-haiku-20240307" "ANTHROPIC_API_KEY"

# Test Groq
echo "3. Testing Groq..."
test_api_key "groq" "llama-3.1-8b-instant" "GROQ_API_KEY"

# Test OpenRouter
echo "4. Testing OpenRouter..."
test_api_key "openrouter" "openai/gpt-3.5-turbo" "OPENROUTER_API_KEY"

# Print summary
echo "📊 SUMMARY OF API KEY TESTS"
echo "============================"
for provider in "${!test_results[@]}"; do
    echo "$provider: ${test_results[$provider]}"
done

echo ""
echo "💡 Note: Some keys might work but with different models or rate limits."
echo "💡 Check the detailed output above for specific error messages."
