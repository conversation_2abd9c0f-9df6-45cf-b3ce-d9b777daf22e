
[38;2;255;255;0mGenerated new master key. Set SINGULARITY_MASTER_KEY environment variable to persist.[0m
Using default SQLite database: /Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/data/nerve.db[0m
Authentication manager initialized[0m
Database initialized successfully[0m
Database tables created successfully[0m
Running analytics tables migration...[0m
Analytics tables already exist, skipping migration[0m
Database initialized successfully[0m
usage: run_ui.py [-h] [--port PORT] [--host HOST]
                 [--cloudflare_tunnel CLOUDFLARE_TUNNEL]
                 [--development DEVELOPMENT]
run_ui.py: error: argument --port: invalid int value: '\x1b[1;33m[WARNING]\x1b[0m'
