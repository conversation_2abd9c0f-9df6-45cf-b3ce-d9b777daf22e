<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<br><span style="color: rgb(255, 255, 0); ">Generated new master key. Set SINGULARITY_MASTER_KEY environment variable to persist.</span><br>
<span style=" ">Authentication manager initialized</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Database tables created successfully</span><br>
<span style=" ">Running analytics tables migration...</span><br>
<span style=" ">Analytics tables already exist, skipping migration</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting job loop...</span><br>
<span style=" ">Starting server...</span><br>
<br><br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<span style=" ">User logged in successfully: admin</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message from admin:</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; Run a full comprehensive diagnosis of yourself and all your tool calls and functions, test subordinate agents, test A2A Agent2Agent Protocol, test MCP Server integrations etc.</span><br>
<span style=" ">Saved chat diagnostic_session for user admin</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 455, in _run_message_loop<br>    result = await self._process_single_iteration(printer)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 478, in _process_single_iteration<br>    prompt = await self.prepare_prompt(loop_data=self.loop_data)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 597, in prepare_prompt<br>    await self.call_extensions(&quot;message_loop_prompts_after&quot;, loop_data=loop_data)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 1009, in call_extensions<br>    await cls(agent=self).execute(**kwargs)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/extensions/message_loop_prompts_after/_91_recall_wait.py&quot;, line 15, in execute<br>    await task<br>  File &quot;/opt/miniconda3/lib/python3.13/asyncio/futures.py&quot;, line 286, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;/opt/miniconda3/lib/python3.13/asyncio/tasks.py&quot;, line 375, in __wakeup<br>    future.result()<br>    ~~~~~~~~~~~~~^^<br>  File &quot;/opt/miniconda3/lib/python3.13/asyncio/futures.py&quot;, line 199, in result<br>    raise self._exception.with_traceback(self._exception_tb)<br>  File &quot;/opt/miniconda3/lib/python3.13/asyncio/tasks.py&quot;, line 304, in __step_run_and_handle_result<br>    result = coro.send(None)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/extensions/message_loop_prompts_after/_50_recall_memories.py&quot;, line 64, in search_memories<br>    query = await self.agent.call_utility_model(<br>            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>    ...&lt;7 lines&gt;...<br>    )<br>    ^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 827, in call_utility_model<br>    async for chunk in (prompt | model).astream({}):<br>    ...&lt;7 lines&gt;...<br>            await callback(content)<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/langchain_core/runnables/base.py&quot;, line 3465, in astream<br>    async for chunk in self.atransform(input_aiter(), config, **kwargs):<br>        yield chunk<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/langchain_core/runnables/base.py&quot;, line 3447, in atransform<br>    async for chunk in self._atransform_stream_with_config(<br>    ...&lt;5 lines&gt;...<br>        yield chunk<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/langchain_core/runnables/base.py&quot;, line 2322, in _atransform_stream_with_config<br>    chunk = await coro_with_context(py_anext(iterator), context)<br>            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/opt/miniconda3/lib/python3.13/asyncio/futures.py&quot;, line 286, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;/opt/miniconda3/lib/python3.13/asyncio/tasks.py&quot;, line 375, in __wakeup<br>    future.result()<br>    ~~~~~~~~~~~~~^^<br><br>&gt;&gt;&gt;  16 stack lines skipped &lt;&lt;&lt;<br><br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/langchain_core/language_models/chat_models.py&quot;, line 592, in astream<br>    async for chunk in self._astream(<br>    ...&lt;11 lines&gt;...<br>        yield chunk.message<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/langchain_openai/chat_models/base.py&quot;, line 2463, in _astream<br>    async for chunk in super()._astream(*args, **kwargs):<br>        yield chunk<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/langchain_openai/chat_models/base.py&quot;, line 1089, in _astream<br>    response = await self.async_client.create(**payload)<br>               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py&quot;, line 2028, in create<br>    return await self._post(<br>           ^^^^^^^^^^^^^^^^^<br>    ...&lt;45 lines&gt;...<br>    )<br>    ^<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/openai/_base_client.py&quot;, line 1742, in post<br>    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)<br>           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/opt/miniconda3/lib/python3.13/site-packages/openai/_base_client.py&quot;, line 1549, in request<br>    raise self._make_status_error_from_response(err.response) from None<br>openai.AuthenticationError: Error code: 401 - {&#x27;error&#x27;: {&#x27;message&#x27;: &#x27;No auth credentials found&#x27;, &#x27;code&#x27;: 401}}<br></span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from Africa/Johannesburg to UTC</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from UTC to Africa/Johannesburg</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for anthropic: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for anthropic: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: API error: Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 153, in get_user_api_key<br>    if cls._is_valid_api_key(user_setting.setting_value):<br>       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 90, in _is_valid_api_key<br>    if not re.match(r&quot;^sk-[A-Za-z0-9_-]+$&quot;, key):<br>           ^^<br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?<br><br>During handling of the above exception, another exception occurred:<br><br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/api.py&quot;, line 64, in handle_request<br>    output = await self.process(input_data, request)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/api/settings_get.py&quot;, line 9, in process<br>    set = settings.convert_out(settings.get_settings())<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/settings.py&quot;, line 475, in convert_out<br>    _get_api_key_field(settings, &quot;openrouter&quot;, &quot;OpenRouter API Key&quot;)<br>    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/settings.py&quot;, line 700, in _get_api_key_field<br>    key = UserAPIKeyManager.get_user_api_key(provider)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 171, in get_user_api_key<br>    if env_key and cls._is_valid_api_key(env_key):<br>                   ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 90, in _is_valid_api_key<br>    if not re.match(r&quot;^sk-[A-Za-z0-9_-]+$&quot;, key):<br>           ^^<br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?<br><br><br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?</span><br>
<span style=" ">Caught signal, stopping server...</span><br>
