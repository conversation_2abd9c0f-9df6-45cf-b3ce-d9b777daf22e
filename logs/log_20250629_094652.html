<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<br><span style="color: rgb(255, 255, 0); ">Generated new master key. Set SINGULARITY_MASTER_KEY environment variable to persist.</span><br>
<span style=" ">Authentication manager initialized</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Database tables created successfully</span><br>
<span style=" ">Running analytics tables migration...</span><br>
<span style=" ">Analytics tables already exist, skipping migration</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting job loop...</span><br>
<span style=" ">Starting server...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message from admin:</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; Run a full comprehensive diagnosis of yourself and all your tool calls and functions, test subordinate agents, test A2A Agent2Agent Protocol, test MCP Server integrations etc.</span><br>
<span style=" ">Saved chat 8162b8e1-c404-4745-9315-ac5df8e1194c for user admin</span><br>
<br><span style="color: rgb(255, 0, 0); ">Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 455, in _run_message_loop<br>    result = await self._process_single_iteration(printer)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 478, in _process_single_iteration<br>    prompt = await self.prepare_prompt(loop_data=self.loop_data)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 597, in prepare_prompt<br>    await self.call_extensions(&quot;message_loop_prompts_after&quot;, loop_data=loop_data)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 1009, in call_extensions<br>    await cls(agent=self).execute(**kwargs)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/extensions/message_loop_prompts_after/_91_recall_wait.py&quot;, line 12, in execute<br>    task = self.agent.get_data(DATA_NAME_TASK_MEMORIES)<br>                               ^^^^^^^^^^^^^^^^^^^^^^^<br>NameError: name &#x27;DATA_NAME_TASK_MEMORIES&#x27; is not defined. Did you mean: &#x27;DATA_NAME_MEMORIES&#x27;?<br><br><br>NameError: name &#x27;DATA_NAME_TASK_MEMORIES&#x27; is not defined. Did you mean: &#x27;DATA_NAME_MEMORIES&#x27;?</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<span style=" ">Caught signal, stopping server...</span><br>
