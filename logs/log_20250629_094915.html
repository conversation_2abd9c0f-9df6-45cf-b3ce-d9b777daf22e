<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<br><span style="color: rgb(255, 255, 0); ">Generated new master key. Set SINGULARITY_MASTER_KEY environment variable to persist.</span><br>
<span style=" ">Using default SQLite database: /Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/data/nerve.db</span><br>
<span style=" ">Authentication manager initialized</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Database tables created successfully</span><br>
<span style=" ">Running analytics tables migration...</span><br>
<span style=" ">Analytics tables already exist, skipping migration</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting job loop...</span><br>
<span style=" ">Starting server...</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for anthropic: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for anthropic: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: API error: Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 153, in get_user_api_key<br>    if cls._is_valid_api_key(user_setting.setting_value):<br>       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 90, in _is_valid_api_key<br>    if not re.match(r&quot;^sk-[A-Za-z0-9_-]+$&quot;, key):<br>           ^^<br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?<br><br>During handling of the above exception, another exception occurred:<br><br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/api.py&quot;, line 64, in handle_request<br>    output = await self.process(input_data, request)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/api/settings_get.py&quot;, line 9, in process<br>    set = settings.convert_out(settings.get_settings())<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/settings.py&quot;, line 475, in convert_out<br>    _get_api_key_field(settings, &quot;openrouter&quot;, &quot;OpenRouter API Key&quot;)<br>    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/settings.py&quot;, line 700, in _get_api_key_field<br>    key = UserAPIKeyManager.get_user_api_key(provider)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 171, in get_user_api_key<br>    if env_key and cls._is_valid_api_key(env_key):<br>                   ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 90, in _is_valid_api_key<br>    if not re.match(r&quot;^sk-[A-Za-z0-9_-]+$&quot;, key):<br>           ^^<br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?<br><br><br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: JSON serialization error: Object of type Response is not JSON serializable</span><br>
<br><br><br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: JSON serialization error: Object of type Response is not JSON serializable</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message from admin:</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; Run a full comprehensive diagnosis of yourself and all your tool calls and functions, test subordinate agents, test A2A Agent2Agent Protocol, test MCP Server integrations etc.</span><br>
<span style=" ">Saved chat 8162b8e1-c404-4745-9315-ac5df8e1194c for user admin</span><br>
<br><span style="color: rgb(255, 0, 0); ">Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 455, in _run_message_loop<br>    result = await self._process_single_iteration(printer)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 478, in _process_single_iteration<br>    prompt = await self.prepare_prompt(loop_data=self.loop_data)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/agent.py&quot;, line 590, in prepare_prompt<br>    await self.call_extensions(&quot;message_loop_prompts_before&quot;, loop_data=loop_data)<br><br>&gt;&gt;&gt;  3 stack lines skipped &lt;&lt;&lt;<br><br>  File &quot;&lt;frozen importlib._bootstrap&gt;&quot;, line 1360, in _find_and_load<br>  File &quot;&lt;frozen importlib._bootstrap&gt;&quot;, line 1331, in _find_and_load_unlocked<br>  File &quot;&lt;frozen importlib._bootstrap&gt;&quot;, line 935, in _load_unlocked<br>  File &quot;&lt;frozen importlib._bootstrap_external&gt;&quot;, line 1026, in exec_module<br>  File &quot;&lt;frozen importlib._bootstrap&gt;&quot;, line 488, in _call_with_frames_removed<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/extensions/message_loop_prompts_before/_90_organize_history_wait.py&quot;, line 4, in &lt;module&gt;<br>    from python.extensions.message_loop_end._10_organize_history import DATA_NAME<br>ImportError: cannot import name &#x27;DATA_NAME&#x27; from &#x27;python.extensions.message_loop_end._10_organize_history&#x27; (/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/extensions/message_loop_end/_10_organize_history.py)<br><br><br>ImportError: cannot import name &#x27;DATA_NAME&#x27; from &#x27;python.extensions.message_loop_end._10_organize_history&#x27; (/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/extensions/message_loop_end/_10_organize_history.py)</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<span style=" ">Caught signal, stopping server...</span><br>
