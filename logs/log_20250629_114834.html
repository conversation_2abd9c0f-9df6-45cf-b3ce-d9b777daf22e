<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<br><span style="color: rgb(255, 255, 0); ">Generated new master key. Set SINGULARITY_MASTER_KEY environment variable to persist.</span><br>
<span style=" ">Using default SQLite database: /Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/data/nerve.db</span><br>
<span style=" ">Authentication manager initialized</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Database tables created successfully</span><br>
<span style=" ">Running analytics tables migration...</span><br>
<span style=" ">Analytics tables already exist, skipping migration</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting job loop...</span><br>
<span style=" ">Starting server...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<br><br><span style="color: rgb(255, 0, 0); ">Error: JSON serialization error: Object of type Response is not JSON serializable</span><br>
<span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for anthropic: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for anthropic: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: name &#x27;re&#x27; is not defined</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: API error: Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 153, in get_user_api_key<br>    if cls._is_valid_api_key(user_setting.setting_value):<br>       ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 90, in _is_valid_api_key<br>    if not re.match(r&quot;^sk-[A-Za-z0-9_-]+$&quot;, key):<br>           ^^<br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?<br><br>During handling of the above exception, another exception occurred:<br><br>Traceback (most recent call last):<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/api.py&quot;, line 64, in handle_request<br>    output = await self.process(input_data, request)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/api/settings_get.py&quot;, line 9, in process<br>    set = settings.convert_out(settings.get_settings())<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/settings.py&quot;, line 475, in convert_out<br>    _get_api_key_field(settings, &quot;openrouter&quot;, &quot;OpenRouter API Key&quot;)<br>    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/settings.py&quot;, line 700, in _get_api_key_field<br>    key = UserAPIKeyManager.get_user_api_key(provider)<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 171, in get_user_api_key<br>    if env_key and cls._is_valid_api_key(env_key):<br>                   ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^<br>  File &quot;/Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/python/helpers/user_api_keys.py&quot;, line 90, in _is_valid_api_key<br>    if not re.match(r&quot;^sk-[A-Za-z0-9_-]+$&quot;, key):<br>           ^^<br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?<br><br><br>NameError: name &#x27;re&#x27; is not defined. Did you forget to import &#x27;re&#x27;?</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: JSON serialization error: Object of type Response is not JSON serializable</span><br>
<span style=" ">Restarting process...</span><br>
