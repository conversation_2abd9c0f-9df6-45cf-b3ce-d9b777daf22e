"""
User-specific API key management for Nerve Agent.

This module handles storing and retrieving AI provider API keys per user,
allowing each user to configure their own API keys through the WebUI.
"""

import os
import re
from typing import Dict, Optional

from flask_login import current_user

from python.helpers.print_style import PrintStyle
from python.models import UserSetting, db


class UserAPIKeyManager:
    """
    Manages user-specific API keys for AI providers.
    """

    # Mapping of API key names to environment variable names
    API_KEY_MAPPING = {
        "openai": "API_KEY_OPENAI",
        "anthropic": "API_KEY_ANTHROPIC",
        "groq": "API_KEY_GROQ",
        "perplexity": "API_KEY_PERPLEXITY",
        "google": "API_KEY_GOOGLE",
        "mistral": "API_KEY_MISTRAL",
        "openrouter": "API_KEY_OPENROUTER",
        "sambanova": "API_KEY_SAMBANOVA",
        "openai_azure": "API_KEY_OPENAI_AZURE",
        "huggingface": "HF_TOKEN",
    }

    @staticmethod
    def _is_valid_api_key(key: str) -> bool:
        """
        Validate if an API key looks legitimate (not a placeholder).

        Updated to support new OpenAI API key formats:
        - sk-proj-... (project keys)
        - sk-None-... (user keys)
        - sk-svcacct-... (service account keys)
        - sk-... (legacy format)

        Args:
            key: API key string to validate

        Returns:
            True if key appears valid, False if it's a placeholder
        """
        if not key or key == "None":
            return False

        # Common placeholder patterns to reject
        placeholder_patterns = [
            "your_",
            "sk-your",
            "api_key_here",
            "replace_with",
            "enter_your",
            "add_your",
            "insert_your",
            "paste_your",
            "example_key",
            "dummy_key",
            "test_key",
            "placeholder",
            "changeme",
            "todo",
            "xxx",
            "yyy",
            "zzz",
        ]

        key_lower = key.lower()
        for pattern in placeholder_patterns:
            if pattern in key_lower:
                return False

        # Enhanced validation for different API key formats

        # OpenAI API keys (all formats)
        if key.startswith(("sk-", "sk-proj-", "sk-None-", "sk-svcacct-")):
            # OpenAI keys should be at least 40 characters and typically 51+ for new formats
            if len(key) < 40:
                return False
            # Should contain alphanumeric characters and allowed symbols
            if not re.match(r"^sk-[A-Za-z0-9_-]+$", key):
                return False
            # Additional check: OpenAI keys typically end with specific patterns
            # Reject keys that look truncated (don't end with proper base64-like characters)
            if key.endswith(("...", "Et", "Ft")) and len(key) < 100:
                return False
            return True

        # Anthropic API keys
        if key.startswith("sk-ant-"):
            if len(key) < 40:
                return False
            return True

        # Google API keys
        if key.startswith("AIza"):
            if len(key) < 30:
                return False
            return True

        # Generic validation for other providers
        # Most API keys are at least 20 characters
        if len(key) < 20:
            return False

        return True

    @classmethod
    def get_user_api_key(
        cls, provider: str, user_id: Optional[int] = None
    ) -> Optional[str]:
        """
        Get API key for a specific provider for the current or specified user.

        Args:
            provider: The AI provider name (e.g., 'openai', 'anthropic')
            user_id: Optional user ID, defaults to current user

        Returns:
            API key string or None if not found
        """
        try:
            # Use current user if no user_id specified
            if user_id is None:
                if not current_user.is_authenticated:
                    # Fall back to environment variable for unauthenticated requests
                    env_key = cls._get_env_api_key(provider)
                    # Only return environment key if it's valid (not a placeholder)
                    if env_key and cls._is_valid_api_key(env_key):
                        return env_key
                    return None
                user_id = current_user.id

            # Get the setting key for this provider
            setting_key = f"api_key_{provider}"

            # Query user setting
            user_setting = UserSetting.query.filter_by(
                user_id=user_id, setting_key=setting_key
            ).first()

            if user_setting and user_setting.setting_value:
                # Validate the stored API key
                if cls._is_valid_api_key(user_setting.setting_value):
                    return user_setting.setting_value
                else:
                    PrintStyle().warning(
                        f"Invalid API key stored for {provider} - appears to be a placeholder"
                    )

            # Fall back to environment variable if user hasn't set their own key
            env_key = cls._get_env_api_key(provider)
            if env_key and cls._is_valid_api_key(env_key):
                return env_key

            return None

        except Exception as e:
            PrintStyle().error(f"Error getting user API key for {provider}: {e}")
            # Fall back to environment variable
            env_key = cls._get_env_api_key(provider)
            if env_key and cls._is_valid_api_key(env_key):
                return env_key
            return None

    @classmethod
    def set_user_api_key(
        cls, provider: str, api_key: str, user_id: Optional[int] = None
    ) -> bool:
        """
        Set API key for a specific provider for the current or specified user.

        Args:
            provider: The AI provider name (e.g., 'openai', 'anthropic')
            api_key: The API key to store
            user_id: Optional user ID, defaults to current user

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use current user if no user_id specified
            if user_id is None:
                if not current_user.is_authenticated:
                    return False
                user_id = current_user.id

            # Get the setting key for this provider
            setting_key = f"api_key_{provider}"

            # Check if setting already exists
            user_setting = UserSetting.query.filter_by(
                user_id=user_id, setting_key=setting_key
            ).first()

            if user_setting:
                # Update existing setting
                user_setting.setting_value = api_key
            else:
                # Create new setting
                user_setting = UserSetting(
                    user_id=user_id, setting_key=setting_key, setting_value=api_key
                )
                db.session.add(user_setting)

            db.session.commit()
            PrintStyle().print(f"Updated API key for {provider} for user {user_id}")
            return True

        except Exception as e:
            db.session.rollback()
            PrintStyle().error(f"Error setting user API key for {provider}: {e}")
            return False

    @classmethod
    def get_all_user_api_keys(cls, user_id: Optional[int] = None) -> Dict[str, str]:
        """
        Get all API keys for the current or specified user.

        Args:
            user_id: Optional user ID, defaults to current user

        Returns:
            Dictionary mapping provider names to API keys
        """
        try:
            # Use current user if no user_id specified
            if user_id is None:
                if not current_user.is_authenticated:
                    return cls._get_all_env_api_keys()
                user_id = current_user.id

            # Get all API key settings for this user
            user_settings = UserSetting.query.filter(
                UserSetting.user_id == user_id,
                UserSetting.setting_key.like("api_key_%"),
            ).all()

            # Build result dictionary
            result = {}

            # Add user-specific keys
            for setting in user_settings:
                provider = setting.setting_key.replace("api_key_", "")
                if setting.setting_value:
                    result[provider] = setting.setting_value

            # Add environment fallbacks for missing keys
            for provider in cls.API_KEY_MAPPING.keys():
                if provider not in result:
                    env_key = cls._get_env_api_key(provider)
                    if env_key:
                        result[provider] = env_key

            return result

        except Exception as e:
            PrintStyle().error(f"Error getting all user API keys: {e}")
            return cls._get_all_env_api_keys()

    @classmethod
    def delete_user_api_key(cls, provider: str, user_id: Optional[int] = None) -> bool:
        """
        Delete API key for a specific provider for the current or specified user.

        Args:
            provider: The AI provider name (e.g., 'openai', 'anthropic')
            user_id: Optional user ID, defaults to current user

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use current user if no user_id specified
            if user_id is None:
                if not current_user.is_authenticated:
                    return False
                user_id = current_user.id

            # Get the setting key for this provider
            setting_key = f"api_key_{provider}"

            # Find and delete the setting
            user_setting = UserSetting.query.filter_by(
                user_id=user_id, setting_key=setting_key
            ).first()

            if user_setting:
                db.session.delete(user_setting)
                db.session.commit()
                PrintStyle().print(f"Deleted API key for {provider} for user {user_id}")
                return True

            return False

        except Exception as e:
            db.session.rollback()
            PrintStyle().error(f"Error deleting user API key for {provider}: {e}")
            return False

    @classmethod
    def _get_env_api_key(cls, provider: str) -> Optional[str]:
        """Get API key from environment variables."""
        env_var = cls.API_KEY_MAPPING.get(provider)
        if env_var:
            return os.getenv(env_var)
        return None

    @classmethod
    def _get_all_env_api_keys(cls) -> Dict[str, str]:
        """Get all API keys from environment variables."""
        result = {}
        for provider, env_var in cls.API_KEY_MAPPING.items():
            key = os.getenv(env_var)
            if key:
                result[provider] = key
        return result
