# OpenCode AI Setup Guide

## ✅ Installation Complete

OpenCode AI v0.0.55 has been successfully installed and configured on your system.

## 📁 Configuration Files

### Global Configuration (`~/.opencode.json`)
- Main configuration with providers, agents, and MCP servers
- Auto compact feature enabled
- Shell integration configured

### Local Configuration (`.opencode.json`)
- Project-specific settings
- Currently configured for basic usage

### Environment Variables (`~/.opencode.env`)
- API keys for all providers
- Shell and system configurations

## 🔑 API Key Setup (REQUIRED)

The provided API keys appear to be invalid. Please update `~/.opencode.env` with valid keys:

```bash
# Edit the environment file
nano ~/.opencode.env

# Update these lines with your real API keys:
export OPENAI_API_KEY="your-real-openai-key"
export ANTHROPIC_API_KEY="your-real-anthropic-key"
export GROQ_API_KEY="your-real-groq-key"
export GEMINI_API_KEY="your-real-google-key"
```

### Where to Get API Keys:
- **OpenAI**: https://platform.openai.com/account/api-keys
- **Anthropic**: https://console.anthropic.com/
- **Groq**: https://console.groq.com/keys
- **Google Gemini**: https://makersuite.google.com/app/apikey

## 🚀 Usage

### Basic Commands
```bash
# Start interactive mode
opencode

# Run single prompt
opencode -p "Your question here"

# Run with JSON output
opencode -p "Your question" -f json

# Debug mode
opencode -d

# Specific directory
opencode -c /path/to/project
```

### Test Installation
```bash
# Source environment and test
source ~/.opencode.env && opencode -p "Hello, are you working?" -f json
```

## 🔧 MCP Servers (Advanced)

MCP servers are configured but disabled due to connection issues. To enable:

1. **Install Node.js/NPM** if not already installed
2. **Enable MCP servers** in `.opencode.json`:

```json
{
  "mcpServers": {
    "memory-bank": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@aakarsh-sasi/memory-bank"],
      "env": []
    }
  }
}
```

### Available MCP Servers:
- **Memory Bank**: Session memory and project state
- **Knowledge Graph**: Long-term pattern recognition
- **Sequential Thinking**: Complex problem solving
- **Desktop Commander**: System access and file operations
- **Exa Search**: Advanced web search
- **Context7**: Documentation and context management

## 🛠 Troubleshooting

### Common Issues:

1. **API Key Errors**: Update `~/.opencode.env` with valid keys
2. **MCP Deadlocks**: Disable MCP servers in config files
3. **Shell Issues**: Reload shell: `source ~/.zshrc`

### Debug Mode:
```bash
opencode -d -p "test prompt"
```

## 📝 Next Steps

1. **Get valid API keys** and update `~/.opencode.env`
2. **Test basic functionality**: `opencode -p "Hello"`
3. **Explore interactive mode**: `opencode`
4. **Enable MCP servers** once basic functionality works
5. **Create custom commands** in `~/.opencode/commands/`

## 🎯 Recommended Configuration

For best results, use **Claude 3.7 Sonnet** or **GPT-4o** as your primary model:

```json
{
  "agents": {
    "coder": {
      "model": "claude-3.7-sonnet",
      "maxTokens": 8000,
      "reasoningEffort": "high"
    }
  }
}
```

## 📚 Documentation

- **Official Docs**: https://github.com/opencode-ai/opencode
- **MCP Registry**: https://smithery.ai/
- **Community**: https://github.com/opencode-ai/opencode/discussions

---

**Status**: ✅ Installed and configured, ⚠️ Needs valid API keys to function
