{"autoCompact": true, "data": {"directory": ".opencode"}, "providers": {"copilot": {"disabled": false}}, "agents": {"coder": {"model": "gpt-4o", "maxTokens": 8000}, "task": {"model": "gpt-4o", "maxTokens": 8000}, "title": {"model": "gpt-4o", "maxTokens": 80}}, "shell": {"path": "/bin/zsh", "args": ["-l"]}, "mcpServers": {}, "lsp": {"go": {"disabled": false, "command": "gopls"}, "typescript": {"disabled": false, "command": "typescript-language-server", "args": ["--st<PERSON>"]}, "python": {"disabled": false, "command": "pylsp"}, "javascript": {"disabled": false, "command": "typescript-language-server", "args": ["--st<PERSON>"]}}}