# OpenCode AI Environment Variables
# Source this file or add these to your shell profile

# API Keys for various providers
export OPENAI_API_KEY="**************************************************************************************************"
export ANTHROPIC_API_KEY="sk-ant-api03-An6KAsM7OVV4qts6RK1Vs95a0CHTzzuZ7a-GRjmt5wC6NhGrXvc6o8Pjh4-UxocUpONm1JB5UhCG2t2sdE"
export GROQ_API_KEY="********************************************************"
export GEMINI_API_KEY="AIzaSyCq2VaZIccj4K8pSDtWWa-1gUZCuRut0wo"
export OPENROUTER_API_KEY="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"

# Additional API Keys (available but not configured in main config)
export MISTRAL_API_KEY="jEyYcslscZHWZhojzNZ2HHSywCRILlXx"
export SAMBANOVA_API_KEY="28422d2e-37f4-4462-afd6-c4039cafa9cf"
export HF_TOKEN="*************************************"

# Shell configuration
export SHELL="/bin/zsh"

# Optional: Set a custom data directory for OpenCode
# export OPENCODE_DATA_DIR="$HOME/.opencode"

# Optional: Enable debug logging
# export OPENCODE_DEBUG=true

# Optional: Custom working directory
# export OPENCODE_WORKDIR="/path/to/your/project"
